<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python Functions - Step by Step Course</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow: hidden;
        }

        .slideshow-container {
            position: relative;
            width: 100%;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .slide {
            display: none;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 60px;
            max-width: 900px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            animation: slideIn 0.5s ease-in-out;
        }

        .slide.active {
            display: block;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        h1 {
            color: #4a5568;
            font-size: 2.5em;
            margin-bottom: 30px;
            text-align: center;
            border-bottom: 3px solid #667eea;
            padding-bottom: 15px;
        }

        h2 {
            color: #2d3748;
            font-size: 2em;
            margin-bottom: 25px;
            text-align: center;
        }

        h3 {
            color: #4a5568;
            font-size: 1.5em;
            margin-bottom: 20px;
            margin-top: 30px;
        }

        p, li {
            font-size: 1.2em;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        ul {
            margin-left: 30px;
            margin-bottom: 20px;
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 1.1em;
            margin: 20px 0;
            overflow-x: auto;
            border-left: 4px solid #667eea;
        }

        .highlight {
            background: #fef5e7;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #f6ad55;
            margin: 20px 0;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .nav-btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .nav-btn:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .slide-counter {
            position: fixed;
            top: 30px;
            right: 30px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px 20px;
            border-radius: 20px;
            font-weight: bold;
            color: #4a5568;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .exercise {
            background: #e6fffa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #38b2ac;
            margin: 20px 0;
        }

        .solution {
            background: #f0fff4;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #48bb78;
            margin: 20px 0;
            display: none;
        }

        .show-solution {
            background: #48bb78;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
        }

        .keyword {
            color: #667eea;
            font-weight: bold;
        }

        .string {
            color: #38a169;
        }

        .comment {
            color: #718096;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="slideshow-container">
        <div class="slide-counter">
            <span id="current-slide">1</span> / <span id="total-slides">28</span>
        </div>

        <!-- Slide 1: Title Slide -->
        <div class="slide active">
            <h1>Python Functions</h1>
            <h2>A Step-by-Step Course</h2>
            <div style="text-align: center; margin-top: 50px;">
                <p style="font-size: 1.4em; color: #4a5568;">Master the fundamentals of Python functions</p>
                <p style="font-size: 1.1em; color: #718096; margin-top: 20px;">From basic syntax to advanced concepts</p>
            </div>
            <div class="highlight">
                <p><strong>What you'll learn:</strong></p>
                <ul>
                    <li>Function definition and calling</li>
                    <li>Parameters and arguments</li>
                    <li>Return values and scope</li>
                    <li>Advanced function concepts</li>
                    <li>Best practices and real examples</li>
                </ul>
            </div>
        </div>

        <!-- Slide 2: What are Functions? -->
        <div class="slide">
            <h2>What are Functions?</h2>
            <p>A <strong>function</strong> is a reusable block of code that performs a specific task.</p>

            <h3>Think of functions like:</h3>
            <ul>
                <li><strong>A recipe</strong> - You give it ingredients (inputs) and get a dish (output)</li>
                <li><strong>A machine</strong> - You put something in, it processes it, and gives you a result</li>
                <li><strong>A tool</strong> - You use it whenever you need to perform a specific task</li>
            </ul>

            <div class="highlight">
                <p><strong>Real-world analogy:</strong></p>
                <p>A coffee machine is like a function:</p>
                <ul>
                    <li><strong>Input:</strong> Coffee beans, water, settings</li>
                    <li><strong>Process:</strong> Brewing</li>
                    <li><strong>Output:</strong> A cup of coffee</li>
                </ul>
            </div>

            <div class="code-block">
<span class="comment"># This is what a simple function looks like</span>
<span class="keyword">def</span> make_coffee(beans, water):
    <span class="comment"># Process the inputs</span>
    coffee = brew(beans, water)
    <span class="keyword">return</span> coffee
            </div>
        </div>

        <!-- Slide 3: Why Use Functions? -->
        <div class="slide">
            <h2>Why Use Functions?</h2>

            <h3>1. 🔄 Reusability</h3>
            <p>Write once, use many times. No need to repeat the same code.</p>

            <h3>2. 📦 Organization</h3>
            <p>Break large programs into smaller, manageable pieces.</p>

            <h3>3. 🐛 Easier Debugging</h3>
            <p>If there's a problem, you know exactly where to look.</p>

            <h3>4. 👥 Collaboration</h3>
            <p>Team members can work on different functions independently.</p>

            <div class="code-block">
<span class="comment"># Without functions - repetitive code</span>
print(<span class="string">"Hello, Alice!"</span>)
print(<span class="string">"Welcome to our program!"</span>)
print(<span class="string">"Hello, Bob!"</span>)
print(<span class="string">"Welcome to our program!"</span>)

<span class="comment"># With functions - clean and reusable</span>
<span class="keyword">def</span> greet_user(name):
    print(<span class="string">f"Hello, {name}!"</span>)
    print(<span class="string">"Welcome to our program!"</span>)

greet_user(<span class="string">"Alice"</span>)
greet_user(<span class="string">"Bob"</span>)
            </div>
        </div>

        <!-- Slide 4: Function Anatomy -->
        <div class="slide">
            <h2>Anatomy of a Function</h2>

            <div class="code-block">
<span class="keyword">def</span> function_name(parameters):
    <span class="string">"""Optional docstring - describes what the function does"""</span>
    <span class="comment"># Function body - the code that runs</span>
    result = <span class="string">"some processing"</span>
    <span class="keyword">return</span> result  <span class="comment"># Optional return statement</span>
            </div>

            <h3>Let's break it down:</h3>
            <ul>
                <li><span class="keyword">def</span> - The keyword that starts a function definition</li>
                <li><strong>function_name</strong> - What you'll call your function (use descriptive names!)</li>
                <li><strong>parameters</strong> - The inputs your function accepts (can be empty)</li>
                <li><strong>:</strong> - Colon to start the function body</li>
                <li><strong>docstring</strong> - Optional description of what the function does</li>
                <li><strong>function body</strong> - The code that runs when the function is called</li>
                <li><span class="keyword">return</span> - Optional statement to send a result back</li>
            </ul>

            <div class="highlight">
                <p><strong>Important:</strong> Python uses indentation (4 spaces) to define what code belongs inside the function!</p>
            </div>
        </div>

        <!-- Slide 5: Your First Function -->
        <div class="slide">
            <h2>Your First Function</h2>
            <p>Let's create a simple function that says hello:</p>

            <div class="code-block">
<span class="comment"># Step 1: Define the function</span>
<span class="keyword">def</span> say_hello():
    print(<span class="string">"Hello, World!"</span>)

<span class="comment"># Step 2: Call the function</span>
say_hello()
            </div>

            <p><strong>Output:</strong></p>
            <div class="code-block">
Hello, World!
            </div>

            <div class="highlight">
                <p><strong>Key Points:</strong></p>
                <ul>
                    <li>Function definition doesn't run the code - it just creates the function</li>
                    <li>You must <strong>call</strong> the function to execute its code</li>
                    <li>Use parentheses <code>()</code> when calling a function</li>
                    <li>This function has no parameters and no return value</li>
                </ul>
            </div>
        </div>

        <!-- Slide 6: Functions with Parameters -->
        <div class="slide">
            <h2>Functions with Parameters</h2>
            <p>Parameters allow functions to accept input values:</p>

            <div class="code-block">
<span class="comment"># Function with one parameter</span>
<span class="keyword">def</span> greet(name):
    print(<span class="string">f"Hello, {name}!"</span>)

<span class="comment"># Function with multiple parameters</span>
<span class="keyword">def</span> introduce(name, age):
    print(<span class="string">f"Hi, I'm {name} and I'm {age} years old."</span>)

<span class="comment"># Calling the functions</span>
greet(<span class="string">"Alice"</span>)
introduce(<span class="string">"Bob"</span>, 25)
            </div>

            <p><strong>Output:</strong></p>
            <div class="code-block">
Hello, Alice!
Hi, I'm Bob and I'm 25 years old.
            </div>

            <div class="exercise">
                <p><strong>Try it yourself:</strong> Create a function called <code>calculate_area</code> that takes length and width as parameters and prints the area.</p>
                <button class="show-solution" onclick="toggleSolution(this)">Show Solution</button>
                <div class="solution">
                    <div class="code-block">
<span class="keyword">def</span> calculate_area(length, width):
    area = length * width
    print(<span class="string">f"The area is {area}"</span>)

calculate_area(5, 3)  <span class="comment"># Output: The area is 15</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 7: Return Values -->
        <div class="slide">
            <h2>Return Values</h2>
            <p>Functions can send results back using the <span class="keyword">return</span> statement:</p>

            <div class="code-block">
<span class="comment"># Function that returns a value</span>
<span class="keyword">def</span> add_numbers(a, b):
    result = a + b
    <span class="keyword">return</span> result

<span class="comment"># Using the returned value</span>
sum_result = add_numbers(5, 3)
print(sum_result)  <span class="comment"># Output: 8</span>

<span class="comment"># You can use the return value directly</span>
print(add_numbers(10, 20))  <span class="comment"># Output: 30</span>
            </div>

            <h3>Functions without return statements</h3>
            <div class="code-block">
<span class="keyword">def</span> print_message():
    print(<span class="string">"This function doesn't return anything"</span>)

result = print_message()
print(result)  <span class="comment"># Output: None</span>
            </div>

            <div class="highlight">
                <p><strong>Remember:</strong> If a function doesn't have a return statement, it automatically returns <code>None</code>.</p>
            </div>
        </div>

        <!-- Slide 8: Multiple Return Values -->
        <div class="slide">
            <h2>Multiple Return Values</h2>
            <p>Python functions can return multiple values at once:</p>

            <div class="code-block">
<span class="keyword">def</span> get_name_age():
    name = <span class="string">"Alice"</span>
    age = 30
    <span class="keyword">return</span> name, age

<span class="comment"># Unpack the returned values</span>
person_name, person_age = get_name_age()
print(person_name)  <span class="comment"># Output: Alice</span>
print(person_age)   <span class="comment"># Output: 30</span>

<span class="comment"># Or get them as a tuple</span>
result = get_name_age()
print(result)       <span class="comment"># Output: ('Alice', 30)</span>
            </div>

            <h3>Practical Example:</h3>
            <div class="code-block">
<span class="keyword">def</span> divide_with_remainder(dividend, divisor):
    quotient = dividend // divisor
    remainder = dividend % divisor
    <span class="keyword">return</span> quotient, remainder

q, r = divide_with_remainder(17, 5)
print(<span class="string">f"17 ÷ 5 = {q} remainder {r}"</span>)
<span class="comment"># Output: 17 ÷ 5 = 3 remainder 2</span>
            </div>
        </div>

        <!-- Slide 9: Positional Arguments -->
        <div class="slide">
            <h2>Positional Arguments</h2>
            <p>Arguments passed to functions in a specific order:</p>

            <div class="code-block">
<span class="keyword">def</span> create_profile(name, age, city):
    print(<span class="string">f"Name: {name}"</span>)
    print(<span class="string">f"Age: {age}"</span>)
    print(<span class="string">f"City: {city}"</span>)

<span class="comment"># Order matters with positional arguments!</span>
create_profile(<span class="string">"Alice"</span>, 25, <span class="string">"New York"</span>)
            </div>

            <p><strong>Output:</strong></p>
            <div class="code-block">
Name: Alice
Age: 25
City: New York
            </div>

            <div class="highlight">
                <p><strong>What happens if we change the order?</strong></p>
                <div class="code-block">
create_profile(25, <span class="string">"Alice"</span>, <span class="string">"New York"</span>)
<span class="comment"># Output:</span>
<span class="comment"># Name: 25</span>
<span class="comment"># Age: Alice</span>
<span class="comment"># City: New York</span>
                </div>
                <p>The function works, but the data is in the wrong places!</p>
            </div>
        </div>

        <!-- Slide 10: Keyword Arguments -->
        <div class="slide">
            <h2>Keyword Arguments</h2>
            <p>Arguments passed by explicitly naming the parameter:</p>

            <div class="code-block">
<span class="keyword">def</span> create_profile(name, age, city):
    print(<span class="string">f"Name: {name}"</span>)
    print(<span class="string">f"Age: {age}"</span>)
    print(<span class="string">f"City: {city}"</span>)

<span class="comment"># Using keyword arguments - order doesn't matter!</span>
create_profile(city=<span class="string">"Boston"</span>, name=<span class="string">"Bob"</span>, age=30)
create_profile(age=22, city=<span class="string">"Chicago"</span>, name=<span class="string">"Carol"</span>)
            </div>

            <h3>Mixing Positional and Keyword Arguments</h3>
            <div class="code-block">
<span class="comment"># Positional arguments must come first</span>
create_profile(<span class="string">"David"</span>, age=28, city=<span class="string">"Seattle"</span>)

<span class="comment"># This would cause an error:</span>
<span class="comment"># create_profile(name="Eve", 35, "Miami")  # SyntaxError!</span>
            </div>

            <div class="highlight">
                <p><strong>Best Practice:</strong> Use keyword arguments for clarity, especially when a function has many parameters.</p>
            </div>
        </div>

        <!-- Slide 11: Default Parameters -->
        <div class="slide">
            <h2>Default Parameters</h2>
            <p>Parameters can have default values if no argument is provided:</p>

            <div class="code-block">
<span class="keyword">def</span> greet(name, greeting=<span class="string">"Hello"</span>, punctuation=<span class="string">"!"</span>):
    print(<span class="string">f"{greeting}, {name}{punctuation}"</span>)

<span class="comment"># Using all defaults</span>
greet(<span class="string">"Alice"</span>)

<span class="comment"># Overriding some defaults</span>
greet(<span class="string">"Bob"</span>, <span class="string">"Hi"</span>)

<span class="comment"># Overriding all defaults</span>
greet(<span class="string">"Carol"</span>, <span class="string">"Hey"</span>, <span class="string">"."</span>)

<span class="comment"># Using keyword arguments to skip parameters</span>
greet(<span class="string">"David"</span>, punctuation=<span class="string">"?"</span>)
            </div>

            <p><strong>Output:</strong></p>
            <div class="code-block">
Hello, Alice!
Hi, Bob!
Hey, Carol.
Hello, David?
            </div>
        </div>

        <!-- Slide 12: Common Parameter Patterns -->
        <div class="slide">
            <h2>Common Parameter Patterns</h2>

            <h3>1. Required + Optional Parameters</h3>
            <div class="code-block">
<span class="keyword">def</span> send_email(recipient, subject, body, cc=<span class="keyword">None</span>, priority=<span class="string">"normal"</span>):
    print(<span class="string">f"To: {recipient}"</span>)
    print(<span class="string">f"Subject: {subject}"</span>)
    print(<span class="string">f"Priority: {priority}"</span>)
    <span class="keyword">if</span> cc:
        print(<span class="string">f"CC: {cc}"</span>)

send_email(<span class="string">"<EMAIL>"</span>, <span class="string">"Meeting"</span>, <span class="string">"See you at 3pm"</span>)
send_email(<span class="string">"<EMAIL>"</span>, <span class="string">"Urgent"</span>, <span class="string">"Call me"</span>, priority=<span class="string">"high"</span>)
            </div>

            <div class="exercise">
                <p><strong>Exercise:</strong> Create a function <code>order_pizza</code> that takes size as required parameter, and toppings and delivery as optional parameters (default: no toppings, pickup).</p>
                <button class="show-solution" onclick="toggleSolution(this)">Show Solution</button>
                <div class="solution">
                    <div class="code-block">
<span class="keyword">def</span> order_pizza(size, toppings=<span class="string">"cheese"</span>, delivery=<span class="keyword">False</span>):
    print(<span class="string">f"Pizza size: {size}"</span>)
    print(<span class="string">f"Toppings: {toppings}"</span>)
    <span class="keyword">if</span> delivery:
        print(<span class="string">"Will be delivered"</span>)
    <span class="keyword">else</span>:
        print(<span class="string">"Ready for pickup"</span>)

order_pizza(<span class="string">"large"</span>)
order_pizza(<span class="string">"medium"</span>, <span class="string">"pepperoni"</span>, <span class="keyword">True</span>)
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 13: Return Statement Deep Dive -->
        <div class="slide">
            <h2>Return Statement Deep Dive</h2>

            <h3>Early Returns</h3>
            <p>You can return from a function at any point:</p>
            <div class="code-block">
<span class="keyword">def</span> check_age(age):
    <span class="keyword">if</span> age < 0:
        <span class="keyword">return</span> <span class="string">"Invalid age"</span>
    <span class="keyword">if</span> age < 18:
        <span class="keyword">return</span> <span class="string">"Minor"</span>
    <span class="keyword">if</span> age < 65:
        <span class="keyword">return</span> <span class="string">"Adult"</span>
    <span class="keyword">return</span> <span class="string">"Senior"</span>

print(check_age(25))  <span class="comment"># Output: Adult</span>
print(check_age(-5))  <span class="comment"># Output: Invalid age</span>
            </div>

            <h3>Return vs Print</h3>
            <div class="code-block">
<span class="keyword">def</span> add_and_print(a, b):
    result = a + b
    print(result)  <span class="comment"># This just displays the value</span>

<span class="keyword">def</span> add_and_return(a, b):
    result = a + b
    <span class="keyword">return</span> result  <span class="comment"># This sends the value back</span>

<span class="comment"># You can't use the result from the first function</span>
value1 = add_and_print(3, 4)  <span class="comment"># Prints 7, but value1 is None</span>

<span class="comment"># You can use the result from the second function</span>
value2 = add_and_return(3, 4)  <span class="comment"># value2 is 7</span>
print(value2 * 2)  <span class="comment"># Output: 14</span>
            </div>
        </div>

        <!-- Slide 14: Variable Scope - Local vs Global -->
        <div class="slide">
            <h2>Variable Scope</h2>
            <p><strong>Scope</strong> determines where variables can be accessed in your code.</p>

            <h3>Local Scope</h3>
            <div class="code-block">
<span class="keyword">def</span> my_function():
    local_var = <span class="string">"I'm local"</span>  <span class="comment"># Only exists inside the function</span>
    print(local_var)

my_function()  <span class="comment"># Output: I'm local</span>
<span class="comment"># print(local_var)  # This would cause an error!</span>
            </div>

            <h3>Global Scope</h3>
            <div class="code-block">
global_var = <span class="string">"I'm global"</span>  <span class="comment"># Exists everywhere</span>

<span class="keyword">def</span> my_function():
    print(global_var)  <span class="comment"># Can access global variables</span>
    local_var = <span class="string">"I'm local"</span>
    print(local_var)

my_function()
print(global_var)  <span class="comment"># This works</span>
            </div>

            <div class="highlight">
                <p><strong>Rule of thumb:</strong> Variables created inside functions are local. Variables created outside functions are global.</p>
            </div>
        </div>

        <!-- Slide 15: Scope Examples and Best Practices -->
        <div class="slide">
            <h2>Scope Examples and Best Practices</h2>

            <h3>Modifying Global Variables</h3>
            <div class="code-block">
counter = 0  <span class="comment"># Global variable</span>

<span class="keyword">def</span> increment_counter():
    <span class="keyword">global</span> counter  <span class="comment"># Tell Python we want to modify the global</span>
    counter += 1

<span class="keyword">def</span> get_counter():
    <span class="keyword">return</span> counter  <span class="comment"># Reading global variables is fine</span>

print(get_counter())  <span class="comment"># Output: 0</span>
increment_counter()
print(get_counter())  <span class="comment"># Output: 1</span>
            </div>

            <h3>Better Approach - Avoid Global Variables</h3>
            <div class="code-block">
<span class="keyword">def</span> increment_value(current_value):
    <span class="keyword">return</span> current_value + 1

counter = 0
counter = increment_value(counter)
print(counter)  <span class="comment"># Output: 1</span>
            </div>

            <div class="highlight">
                <p><strong>Best Practice:</strong> Avoid global variables when possible. Pass values as parameters and return results instead.</p>
            </div>
        </div>

        <!-- Slide 16: Variable Lifetime -->
        <div class="slide">
            <h2>Variable Lifetime</h2>
            <p>Variables have different lifetimes depending on where they're created:</p>

            <div class="code-block">
<span class="keyword">def</span> demonstrate_lifetime():
    print(<span class="string">"Function starts"</span>)
    temp_var = <span class="string">"I exist only during function execution"</span>
    print(temp_var)
    print(<span class="string">"Function ends"</span>)
    <span class="comment"># temp_var is destroyed when function ends</span>

demonstrate_lifetime()
<span class="comment"># temp_var no longer exists here</span>
            </div>

            <h3>Each Function Call Creates New Variables</h3>
            <div class="code-block">
<span class="keyword">def</span> count_calls():
    call_count = 0  <span class="comment"># New variable each time</span>
    call_count += 1
    print(<span class="string">f"Call count: {call_count}"</span>)

count_calls()  <span class="comment"># Output: Call count: 1</span>
count_calls()  <span class="comment"># Output: Call count: 1 (not 2!)</span>
count_calls()  <span class="comment"># Output: Call count: 1 (still not 3!)</span>
            </div>

            <div class="highlight">
                <p><strong>Key Point:</strong> Local variables are created fresh each time a function is called and destroyed when the function ends.</p>
            </div>
        </div>

        <!-- Slide 17: Parameter Scope -->
        <div class="slide">
            <h2>Parameter Scope</h2>
            <p>Function parameters are also local variables:</p>

            <div class="code-block">
name = <span class="string">"Global Alice"</span>

<span class="keyword">def</span> greet(name):  <span class="comment"># This 'name' is different from global 'name'</span>
    print(<span class="string">f"Hello, {name}!"</span>)
    name = <span class="string">"Modified inside function"</span>
    print(<span class="string">f"Inside function: {name}"</span>)

print(<span class="string">f"Before function: {name}"</span>)
greet(<span class="string">"Local Bob"</span>)
print(<span class="string">f"After function: {name}"</span>)
            </div>

            <p><strong>Output:</strong></p>
            <div class="code-block">
Before function: Global Alice
Hello, Local Bob!
Inside function: Modified inside function
After function: Global Alice
            </div>

            <div class="highlight">
                <p><strong>Important:</strong> Modifying parameters inside a function doesn't affect variables outside the function.</p>
            </div>
        </div>

        <!-- Slide 18: Scope Resolution (LEGB Rule) -->
        <div class="slide">
            <h2>Scope Resolution - LEGB Rule</h2>
            <p>Python searches for variables in this order:</p>

            <ul>
                <li><strong>L</strong>ocal - Inside the current function</li>
                <li><strong>E</strong>nclosing - In any outer function</li>
                <li><strong>G</strong>lobal - At the module level</li>
                <li><strong>B</strong>uilt-in - Built-in names like print, len, etc.</li>
            </ul>

            <div class="code-block">
x = <span class="string">"Global"</span>

<span class="keyword">def</span> outer_function():
    x = <span class="string">"Enclosing"</span>

    <span class="keyword">def</span> inner_function():
        x = <span class="string">"Local"</span>
        print(<span class="string">f"Inner sees: {x}"</span>)  <span class="comment"># Local wins</span>

    inner_function()
    print(<span class="string">f"Outer sees: {x}"</span>)  <span class="comment"># Enclosing wins</span>

outer_function()
print(<span class="string">f"Global sees: {x}"</span>)  <span class="comment"># Global wins</span>
            </div>

            <p><strong>Output:</strong></p>
            <div class="code-block">
Inner sees: Local
Outer sees: Enclosing
Global sees: Global
            </div>
        </div>

        <!-- Slide 19: *args - Variable Number of Arguments -->
        <div class="slide">
            <h2>*args - Variable Number of Arguments</h2>
            <p>Sometimes you don't know how many arguments a function will receive:</p>

            <div class="code-block">
<span class="keyword">def</span> sum_all(*args):
    <span class="string">"""Sum any number of arguments"""</span>
    total = 0
    <span class="keyword">for</span> num <span class="keyword">in</span> args:
        total += num
    <span class="keyword">return</span> total

<span class="comment"># Can call with any number of arguments</span>
print(sum_all(1, 2, 3))           <span class="comment"># Output: 6</span>
print(sum_all(10, 20, 30, 40))   <span class="comment"># Output: 100</span>
print(sum_all(5))                 <span class="comment"># Output: 5</span>
print(sum_all())                  <span class="comment"># Output: 0</span>
            </div>

            <h3>What is *args?</h3>
            <div class="code-block">
<span class="keyword">def</span> show_args(*args):
    print(<span class="string">f"args is a tuple: {args}"</span>)
    print(<span class="string">f"Type: {type(args)}"</span>)

show_args(1, 2, 3, <span class="string">"hello"</span>)
<span class="comment"># Output:</span>
<span class="comment"># args is a tuple: (1, 2, 3, 'hello')</span>
<span class="comment"># Type: &lt;class 'tuple'&gt;</span>
            </div>

            <div class="highlight">
                <p><strong>Note:</strong> The name "args" is just a convention. You could use *numbers, *values, etc.</p>
            </div>
        </div>

        <!-- Slide 20: **kwargs - Keyword Arguments -->
        <div class="slide">
            <h2>**kwargs - Keyword Arguments</h2>
            <p>Accept any number of keyword arguments:</p>

            <div class="code-block">
<span class="keyword">def</span> create_user(**kwargs):
    <span class="string">"""Create a user with any number of attributes"""</span>
    print(<span class="string">"Creating user with:"</span>)
    <span class="keyword">for</span> key, value <span class="keyword">in</span> kwargs.items():
        print(<span class="string">f"  {key}: {value}"</span>)

create_user(name=<span class="string">"Alice"</span>, age=30, city=<span class="string">"Boston"</span>)
create_user(username=<span class="string">"bob123"</span>, email=<span class="string">"<EMAIL>"</span>, active=<span class="keyword">True</span>)
            </div>

            <p><strong>Output:</strong></p>
            <div class="code-block">
Creating user with:
  name: Alice
  age: 30
  city: Boston
Creating user with:
  username: bob123
  email: <EMAIL>
  active: True
            </div>

            <h3>What is **kwargs?</h3>
            <div class="code-block">
<span class="keyword">def</span> show_kwargs(**kwargs):
    print(<span class="string">f"kwargs is a dictionary: {kwargs}"</span>)
    print(<span class="string">f"Type: {type(kwargs)}"</span>)

show_kwargs(a=1, b=2, name=<span class="string">"test"</span>)
<span class="comment"># Output:</span>
<span class="comment"># kwargs is a dictionary: {'a': 1, 'b': 2, 'name': 'test'}</span>
<span class="comment"># Type: &lt;class 'dict'&gt;</span>
            </div>
        </div>

        <!-- Slide 21: Combining All Parameter Types -->
        <div class="slide">
            <h2>Combining All Parameter Types</h2>
            <p>You can combine regular parameters, *args, and **kwargs:</p>

            <div class="code-block">
<span class="keyword">def</span> flexible_function(required, default=<span class="string">"default"</span>, *args, **kwargs):
    print(<span class="string">f"Required: {required}"</span>)
    print(<span class="string">f"Default: {default}"</span>)
    print(<span class="string">f"Args: {args}"</span>)
    print(<span class="string">f"Kwargs: {kwargs}"</span>)

flexible_function(<span class="string">"must_have"</span>, <span class="string">"custom"</span>, 1, 2, 3, name=<span class="string">"Alice"</span>, age=30)
            </div>

            <p><strong>Output:</strong></p>
            <div class="code-block">
Required: must_have
Default: custom
Args: (1, 2, 3)
Kwargs: {'name': 'Alice', 'age': 30}
            </div>

            <div class="highlight">
                <p><strong>Order matters:</strong> regular parameters, *args, **kwargs</p>
            </div>

            <div class="exercise">
                <p><strong>Exercise:</strong> Create a function <code>log_message</code> that takes a required message, optional level (default: "INFO"), and any additional metadata as keyword arguments.</p>
                <button class="show-solution" onclick="toggleSolution(this)">Show Solution</button>
                <div class="solution">
                    <div class="code-block">
<span class="keyword">def</span> log_message(message, level=<span class="string">"INFO"</span>, **metadata):
    print(<span class="string">f"[{level}] {message}"</span>)
    <span class="keyword">if</span> metadata:
        print(<span class="string">f"Metadata: {metadata}"</span>)

log_message(<span class="string">"User logged in"</span>)
log_message(<span class="string">"Error occurred"</span>, <span class="string">"ERROR"</span>, user_id=123, ip=<span class="string">"***********"</span>)
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 22: Lambda Functions -->
        <div class="slide">
            <h2>Lambda Functions</h2>
            <p><strong>Lambda functions</strong> are small, anonymous functions defined inline:</p>

            <div class="code-block">
<span class="comment"># Regular function</span>
<span class="keyword">def</span> square(x):
    <span class="keyword">return</span> x ** 2

<span class="comment"># Lambda function (same thing)</span>
square_lambda = <span class="keyword">lambda</span> x: x ** 2

print(square(5))        <span class="comment"># Output: 25</span>
print(square_lambda(5)) <span class="comment"># Output: 25</span>
            </div>

            <h3>Lambda with Multiple Parameters</h3>
            <div class="code-block">
<span class="comment"># Lambda with multiple parameters</span>
add = <span class="keyword">lambda</span> x, y: x + y
multiply = <span class="keyword">lambda</span> x, y, z: x * y * z

print(add(3, 4))        <span class="comment"># Output: 7</span>
print(multiply(2, 3, 4)) <span class="comment"># Output: 24</span>
            </div>

            <h3>Common Use Cases</h3>
            <div class="code-block">
<span class="comment"># Sorting with custom key</span>
students = [(<span class="string">"Alice"</span>, 85), (<span class="string">"Bob"</span>, 90), (<span class="string">"Carol"</span>, 78)]
students.sort(key=<span class="keyword">lambda</span> student: student[1])  <span class="comment"># Sort by grade</span>
print(students)  <span class="comment"># [('Carol', 78), ('Alice', 85), ('Bob', 90)]</span>

<span class="comment"># Filtering with lambda</span>
numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
evens = list(filter(<span class="keyword">lambda</span> x: x % 2 == 0, numbers))
print(evens)  <span class="comment"># [2, 4, 6, 8, 10]</span>
            </div>

            <div class="highlight">
                <p><strong>When to use lambda:</strong> For simple, one-line functions that you use once. For complex logic, use regular functions.</p>
            </div>
        </div>

        <!-- Slide 23: Function Documentation -->
        <div class="slide">
            <h2>Function Documentation</h2>
            <p>Good functions have clear documentation:</p>

            <div class="code-block">
<span class="keyword">def</span> calculate_bmi(weight, height):
    <span class="string">"""
    Calculate Body Mass Index (BMI).

    Args:
        weight (float): Weight in kilograms
        height (float): Height in meters

    Returns:
        float: BMI value

    Example:
        >>> calculate_bmi(70, 1.75)
        22.86
    """</span>
    <span class="keyword">if</span> height <= 0:
        <span class="keyword">raise</span> ValueError(<span class="string">"Height must be positive"</span>)

    bmi = weight / (height ** 2)
    <span class="keyword">return</span> round(bmi, 2)

<span class="comment"># Access the documentation</span>
print(calculate_bmi.__doc__)
help(calculate_bmi)
            </div>

            <h3>Best Practices for Documentation</h3>
            <ul>
                <li>Describe what the function does</li>
                <li>List parameters and their types</li>
                <li>Describe the return value</li>
                <li>Include examples when helpful</li>
                <li>Mention any exceptions that might be raised</li>
            </ul>
        </div>

        <!-- Slide 24: Practice Exercise 1 -->
        <div class="slide">
            <h2>Practice Exercise 1: Temperature Converter</h2>

            <div class="exercise">
                <p><strong>Challenge:</strong> Create a function that converts temperatures between Celsius and Fahrenheit.</p>

                <p><strong>Requirements:</strong></p>
                <ul>
                    <li>Function name: <code>convert_temperature</code></li>
                    <li>Parameters: <code>temp</code> (number), <code>from_unit</code> (string), <code>to_unit</code> (string)</li>
                    <li>Return the converted temperature</li>
                    <li>Handle both "C" and "F" units</li>
                </ul>

                <p><strong>Formulas:</strong></p>
                <ul>
                    <li>C to F: (C × 9/5) + 32</li>
                    <li>F to C: (F - 32) × 5/9</li>
                </ul>

                <p><strong>Test cases:</strong></p>
                <div class="code-block">
print(convert_temperature(0, <span class="string">"C"</span>, <span class="string">"F"</span>))    <span class="comment"># Should output: 32.0</span>
print(convert_temperature(100, <span class="string">"C"</span>, <span class="string">"F"</span>))  <span class="comment"># Should output: 212.0</span>
print(convert_temperature(32, <span class="string">"F"</span>, <span class="string">"C"</span>))   <span class="comment"># Should output: 0.0</span>
                </div>

                <button class="show-solution" onclick="toggleSolution(this)">Show Solution</button>
                <div class="solution">
                    <div class="code-block">
<span class="keyword">def</span> convert_temperature(temp, from_unit, to_unit):
    <span class="string">"""Convert temperature between Celsius and Fahrenheit."""</span>
    <span class="keyword">if</span> from_unit == to_unit:
        <span class="keyword">return</span> temp

    <span class="keyword">if</span> from_unit == <span class="string">"C"</span> <span class="keyword">and</span> to_unit == <span class="string">"F"</span>:
        <span class="keyword">return</span> (temp * 9/5) + 32
    <span class="keyword">elif</span> from_unit == <span class="string">"F"</span> <span class="keyword">and</span> to_unit == <span class="string">"C"</span>:
        <span class="keyword">return</span> (temp - 32) * 5/9
    <span class="keyword">else</span>:
        <span class="keyword">return</span> <span class="string">"Invalid units"</span>

<span class="comment"># Test the function</span>
print(convert_temperature(0, <span class="string">"C"</span>, <span class="string">"F"</span>))    <span class="comment"># 32.0</span>
print(convert_temperature(100, <span class="string">"C"</span>, <span class="string">"F"</span>))  <span class="comment"># 212.0</span>
print(convert_temperature(32, <span class="string">"F"</span>, <span class="string">"C"</span>))   <span class="comment"># 0.0</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 25: Practice Exercise 2 -->
        <div class="slide">
            <h2>Practice Exercise 2: Grade Calculator</h2>

            <div class="exercise">
                <p><strong>Challenge:</strong> Create a function that calculates final grades.</p>

                <p><strong>Requirements:</strong></p>
                <ul>
                    <li>Function name: <code>calculate_grade</code></li>
                    <li>Accept any number of test scores using *args</li>
                    <li>Optional parameters: <code>drop_lowest=False</code>, <code>extra_credit=0</code></li>
                    <li>Return both numerical average and letter grade</li>
                </ul>

                <p><strong>Grading scale:</strong> A: 90+, B: 80-89, C: 70-79, D: 60-69, F: <60</p>

                <button class="show-solution" onclick="toggleSolution(this)">Show Solution</button>
                <div class="solution">
                    <div class="code-block">
<span class="keyword">def</span> calculate_grade(*scores, drop_lowest=<span class="keyword">False</span>, extra_credit=0):
    <span class="string">"""Calculate final grade from test scores."""</span>
    <span class="keyword">if</span> <span class="keyword">not</span> scores:
        <span class="keyword">return</span> 0, <span class="string">"F"</span>

    score_list = list(scores)

    <span class="keyword">if</span> drop_lowest <span class="keyword">and</span> len(score_list) > 1:
        score_list.remove(min(score_list))

    average = sum(score_list) / len(score_list) + extra_credit

    <span class="keyword">if</span> average >= 90:
        letter = <span class="string">"A"</span>
    <span class="keyword">elif</span> average >= 80:
        letter = <span class="string">"B"</span>
    <span class="keyword">elif</span> average >= 70:
        letter = <span class="string">"C"</span>
    <span class="keyword">elif</span> average >= 60:
        letter = <span class="string">"D"</span>
    <span class="keyword">else</span>:
        letter = <span class="string">"F"</span>

    <span class="keyword">return</span> round(average, 1), letter

<span class="comment"># Test the function</span>
print(calculate_grade(85, 92, 78, 88))  <span class="comment"># (85.8, 'B')</span>
print(calculate_grade(85, 92, 78, 88, drop_lowest=<span class="keyword">True</span>))  <span class="comment"># (88.3, 'B')</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 26: Best Practices Summary -->
        <div class="slide">
            <h2>Function Best Practices</h2>

            <h3>✅ Do:</h3>
            <ul>
                <li><strong>Use descriptive names:</strong> <code>calculate_tax()</code> not <code>calc()</code></li>
                <li><strong>Keep functions small:</strong> One function, one purpose</li>
                <li><strong>Use docstrings:</strong> Document what your function does</li>
                <li><strong>Return values:</strong> Instead of printing inside functions</li>
                <li><strong>Use default parameters:</strong> For optional arguments</li>
                <li><strong>Handle edge cases:</strong> What if inputs are invalid?</li>
            </ul>

            <h3>❌ Avoid:</h3>
            <ul>
                <li><strong>Global variables:</strong> Pass parameters instead</li>
                <li><strong>Too many parameters:</strong> Consider using a dictionary or class</li>
                <li><strong>Side effects:</strong> Functions should be predictable</li>
                <li><strong>Cryptic names:</strong> <code>a</code>, <code>b</code>, <code>temp</code> without context</li>
            </ul>

            <div class="code-block">
<span class="comment"># Good function</span>
<span class="keyword">def</span> calculate_compound_interest(principal, rate, time, compounds_per_year=1):
    <span class="string">"""Calculate compound interest."""</span>
    <span class="keyword">return</span> principal * (1 + rate/compounds_per_year) ** (compounds_per_year * time)

<span class="comment"># Not so good function</span>
<span class="keyword">def</span> calc(p, r, t):
    <span class="keyword">global</span> result
    result = p * (1 + r) ** t
    print(result)
            </div>
        </div>

        <!-- Slide 27: Key Takeaways -->
        <div class="slide">
            <h2>Key Takeaways</h2>

            <div class="highlight">
                <h3>🎯 What You've Learned:</h3>
                <ul>
                    <li><strong>Function Basics:</strong> Definition, calling, parameters, return values</li>
                    <li><strong>Parameter Types:</strong> Positional, keyword, default, *args, **kwargs</li>
                    <li><strong>Scope:</strong> Local vs global variables, LEGB rule</li>
                    <li><strong>Advanced Concepts:</strong> Lambda functions, documentation</li>
                    <li><strong>Best Practices:</strong> Clean, readable, maintainable code</li>
                </ul>
            </div>

            <h3>🚀 Next Steps:</h3>
            <ul>
                <li>Practice writing functions for real problems</li>
                <li>Learn about decorators and generators</li>
                <li>Explore functional programming concepts</li>
                <li>Study object-oriented programming (classes and methods)</li>
                <li>Learn about error handling in functions</li>
            </ul>

            <div class="code-block">
<span class="comment"># Remember: Functions are the building blocks of larger programs!</span>
<span class="keyword">def</span> your_programming_journey():
    <span class="keyword">return</span> <span class="string">"Keep practicing and building amazing things!"</span>

print(your_programming_journey())
            </div>
        </div>

        <!-- Slide 28: Additional Resources -->
        <div class="slide">
            <h2>Additional Resources</h2>

            <h3>📚 Further Reading:</h3>
            <ul>
                <li><strong>Python Official Documentation:</strong> docs.python.org</li>
                <li><strong>Real Python:</strong> realpython.com/python-functions/</li>
                <li><strong>Automate the Boring Stuff:</strong> automatetheboringstuff.com</li>
                <li><strong>Python Tricks:</strong> Book by Dan Bader</li>
            </ul>

            <h3>💻 Practice Platforms:</h3>
            <ul>
                <li><strong>LeetCode:</strong> Algorithm practice</li>
                <li><strong>HackerRank:</strong> Python challenges</li>
                <li><strong>Codewars:</strong> Coding kata</li>
                <li><strong>Project Euler:</strong> Mathematical problems</li>
            </ul>

            <h3>🎓 What's Next:</h3>
            <ul>
                <li>Object-Oriented Programming (Classes)</li>
                <li>Error Handling (try/except)</li>
                <li>File I/O and Data Processing</li>
                <li>Web Development with Flask/Django</li>
                <li>Data Science with pandas/numpy</li>
            </ul>

            <div style="text-align: center; margin-top: 40px;">
                <h2 style="color: #667eea;">🎉 Congratulations!</h2>
                <p style="font-size: 1.3em;">You've completed the Python Functions course!</p>
                <p style="color: #718096;">Now go build something amazing! 🚀</p>
            </div>
        </div>

        <!-- Navigation -->
        <div class="navigation">
            <button class="nav-btn" id="prev-btn" onclick="changeSlide(-1)">← Previous</button>
            <button class="nav-btn" id="next-btn" onclick="changeSlide(1)">Next →</button>
        </div>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        
        document.getElementById('total-slides').textContent = totalSlides;

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            
            document.getElementById('current-slide').textContent = currentSlide + 1;
            
            // Update navigation buttons
            document.getElementById('prev-btn').disabled = currentSlide === 0;
            document.getElementById('next-btn').disabled = currentSlide === totalSlides - 1;
        }

        function changeSlide(direction) {
            if (direction === 1 && currentSlide < totalSlides - 1) {
                showSlide(currentSlide + 1);
            } else if (direction === -1 && currentSlide > 0) {
                showSlide(currentSlide - 1);
            }
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                changeSlide(1);
            } else if (e.key === 'ArrowLeft') {
                changeSlide(-1);
            }
        });

        // Initialize
        showSlide(0);

        // Solution toggle function
        function toggleSolution(button) {
            const solution = button.nextElementSibling;
            if (solution.style.display === 'none' || solution.style.display === '') {
                solution.style.display = 'block';
                button.textContent = 'Hide Solution';
            } else {
                solution.style.display = 'none';
                button.textContent = 'Show Solution';
            }
        }
    </script>
</body>
</html>
